<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Placeholder Image Generator</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .generator {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #1f67f1;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background: #1557d1;
        }
        .preview {
            margin-top: 30px;
            text-align: center;
        }
        .preview canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            max-width: 100%;
        }
        .download-link {
            display: inline-block;
            margin-top: 15px;
            padding: 10px 20px;
            background: #10b981;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .download-link:hover {
            background: #059669;
        }
        .instructions {
            background: #e0f2fe;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #1f67f1;
        }
    </style>
</head>
<body>
    <div class="generator">
        <h1>🎨 Placeholder Image Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <p>Use this tool to generate placeholder images for your Villo portfolio template. Create images for:</p>
            <ul>
                <li><strong>Hero Avatar:</strong> 400x400px (square)</li>
                <li><strong>Portfolio Projects:</strong> 600x450px (4:3 ratio)</li>
                <li><strong>Favicon:</strong> 32x32px (square)</li>
            </ul>
        </div>

        <div class="form-group">
            <label for="width">Width (px):</label>
            <input type="number" id="width" value="600" min="32" max="2000">
        </div>

        <div class="form-group">
            <label for="height">Height (px):</label>
            <input type="number" id="height" value="450" min="32" max="2000">
        </div>

        <div class="form-group">
            <label for="text">Text:</label>
            <input type="text" id="text" value="Portfolio Image" maxlength="50">
        </div>

        <div class="form-group">
            <label for="bgColor">Background Color:</label>
            <input type="color" id="bgColor" value="#1f67f1">
        </div>

        <div class="form-group">
            <label for="textColor">Text Color:</label>
            <input type="color" id="textColor" value="#ffffff">
        </div>

        <div class="form-group">
            <label for="preset">Quick Presets:</label>
            <select id="preset">
                <option value="">Custom</option>
                <option value="hero">Hero Avatar (400x400)</option>
                <option value="portfolio">Portfolio Project (600x450)</option>
                <option value="favicon">Favicon (32x32)</option>
            </select>
        </div>

        <button onclick="generateImage()">Generate Image</button>

        <div class="preview" id="preview" style="display: none;">
            <h3>Preview:</h3>
            <canvas id="canvas"></canvas>
            <br>
            <a id="downloadLink" class="download-link" download="placeholder.png">Download Image</a>
        </div>
    </div>

    <script>
        // Preset configurations
        const presets = {
            hero: { width: 400, height: 400, text: 'Your Photo', bgColor: '#1f67f1' },
            portfolio: { width: 600, height: 450, text: 'Project Image', bgColor: '#e0f11f' },
            favicon: { width: 32, height: 32, text: 'V', bgColor: '#1f67f1' }
        };

        // Handle preset selection
        document.getElementById('preset').addEventListener('change', function() {
            const preset = presets[this.value];
            if (preset) {
                document.getElementById('width').value = preset.width;
                document.getElementById('height').value = preset.height;
                document.getElementById('text').value = preset.text;
                document.getElementById('bgColor').value = preset.bgColor;
            }
        });

        function generateImage() {
            const width = parseInt(document.getElementById('width').value);
            const height = parseInt(document.getElementById('height').value);
            const text = document.getElementById('text').value;
            const bgColor = document.getElementById('bgColor').value;
            const textColor = document.getElementById('textColor').value;

            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = width;
            canvas.height = height;

            // Fill background
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, width, height);

            // Add gradient overlay
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, 'rgba(255,255,255,0.1)');
            gradient.addColorStop(1, 'rgba(0,0,0,0.1)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);

            // Calculate font size based on canvas size
            const fontSize = Math.max(12, Math.min(width, height) / 8);
            ctx.font = `bold ${fontSize}px Arial, sans-serif`;
            ctx.fillStyle = textColor;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // Add text
            ctx.fillText(text, width / 2, height / 2);

            // Add dimensions text
            const dimText = `${width}×${height}`;
            const dimFontSize = Math.max(8, fontSize / 3);
            ctx.font = `${dimFontSize}px Arial, sans-serif`;
            ctx.fillStyle = textColor;
            ctx.globalAlpha = 0.7;
            ctx.fillText(dimText, width / 2, height / 2 + fontSize / 2 + dimFontSize);
            ctx.globalAlpha = 1;

            // Show preview
            document.getElementById('preview').style.display = 'block';

            // Create download link
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const downloadLink = document.getElementById('downloadLink');
                downloadLink.href = url;
                downloadLink.download = `${text.toLowerCase().replace(/\s+/g, '-')}-${width}x${height}.png`;
            });
        }

        // Generate initial image
        generateImage();
    </script>
</body>
</html>
