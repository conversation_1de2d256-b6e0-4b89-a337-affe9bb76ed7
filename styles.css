/* ===== CSS VARIABLES ===== */
:root {
  /* Colors */
  --primary-color: #1f67f1;
  --primary-light: #e0f11f;
  --secondary-color: #f0f0f0;
  --accent-color: #121212;
  --text-primary: #121212;
  --text-secondary: #666666;
  --text-light: #999999;
  --background-light: #ffffff;
  --background-dark: #f8f9fa;
  --border-color: #e5e5e5;
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);

  /* Typography */
  --font-primary: 'D<PERSON> Sans', sans-serif;
  --font-secondary: 'Inter', sans-serif;
  --font-accent: 'Manrope', sans-serif;

  /* Font Sizes */
  --fs-xs: 0.75rem;
  --fs-sm: 0.875rem;
  --fs-base: 1rem;
  --fs-lg: 1.125rem;
  --fs-xl: 1.25rem;
  --fs-2xl: 1.5rem;
  --fs-3xl: 1.875rem;
  --fs-4xl: 2.25rem;
  --fs-5xl: 3rem;
  --fs-6xl: 3.75rem;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-2xl: 4rem;
  --spacing-3xl: 6rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* Dark mode variables */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #e0f11f;
    --secondary-color: #121212;
    --accent-color: #f0f0f0;
    --text-primary: #f0f0f0;
    --text-secondary: #cccccc;
    --text-light: #999999;
    --background-light: #121212;
    --background-dark: #1a1a1a;
    --border-color: #333333;
  }
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  font-size: var(--fs-base);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background-light);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition-base);
}

ul {
  list-style: none;
}

button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-full);
  font-weight: 500;
  font-size: var(--fs-base);
  text-decoration: none;
  transition: var(--transition-base);
  cursor: pointer;
  border: 2px solid transparent;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: transparent;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-secondary {
  background-color: transparent;
  border-color: var(--text-primary);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background-color: var(--text-primary);
  color: var(--background-light);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.section-title {
  font-family: var(--font-accent);
  font-size: var(--fs-4xl);
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.section-subtitle {
  font-size: var(--fs-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: var(--z-fixed);
  transition: var(--transition-base);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-logo a {
  font-family: var(--font-accent);
  font-size: var(--fs-2xl);
  font-weight: 500;
  color: var(--text-primary);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-link {
  font-weight: 500;
  color: var(--text-secondary);
  transition: var(--transition-base);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.bar {
  width: 25px;
  height: 3px;
  background-color: var(--text-primary);
  transition: var(--transition-base);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: var(--spacing-3xl) 0;
  position: relative;
  overflow: hidden;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.hero-content {
  animation: fadeInUp 1s ease-out;
}

.hero-title {
  margin-bottom: var(--spacing-lg);
}

.hero-greeting {
  display: block;
  font-size: var(--fs-xl);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.hero-name {
  display: block;
  font-size: var(--fs-6xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.hero-role {
  display: block;
  font-size: var(--fs-3xl);
  color: var(--primary-color);
  font-weight: 500;
}

.hero-description {
  font-size: var(--fs-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  max-width: 500px;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.hero-image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInRight 1s ease-out 0.3s both;
}

.hero-avatar {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  z-index: 2;
  box-shadow: var(--shadow-heavy);
}

.hero-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-decoration {
  position: absolute;
  inset: 0;
}

.decoration-circle {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  border: 3px solid var(--primary-color);
  border-radius: 50%;
  opacity: 0.3;
  animation: float 3s ease-in-out infinite;
}

.decoration-dots {
  position: absolute;
  bottom: -10px;
  left: -10px;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, var(--primary-color) 2px, transparent 2px);
  background-size: 15px 15px;
  opacity: 0.4;
  animation: float 3s ease-in-out infinite reverse;
}

.hero-scroll {
  position: absolute;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  animation: fadeIn 1s ease-out 1s both;
}

.scroll-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: var(--fs-sm);
  transition: var(--transition-base);
}

.scroll-indicator:hover {
  color: var(--primary-color);
}

.scroll-arrow {
  width: 20px;
  height: 20px;
  border-right: 2px solid currentColor;
  border-bottom: 2px solid currentColor;
  transform: rotate(45deg);
  animation: bounce 2s infinite;
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background-dark);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: start;
}

.about-text h3 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.about-text p {
  font-size: var(--fs-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--fs-4xl);
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: var(--fs-sm);
  color: var(--text-secondary);
}

.about-skills h4 {
  font-size: var(--fs-2xl);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
}

.skill-item {
  padding: var(--spacing-lg);
  background-color: var(--background-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-light);
  transition: var(--transition-base);
}

.skill-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.skill-icon {
  font-size: var(--fs-4xl);
  margin-bottom: var(--spacing-md);
}

.skill-item h5 {
  font-size: var(--fs-xl);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.skill-item p {
  color: var(--text-secondary);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) rotate(45deg);
  }
  40% {
    transform: translateY(-5px) rotate(45deg);
  }
  60% {
    transform: translateY(-3px) rotate(45deg);
  }
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio {
  padding: var(--spacing-3xl) 0;
}

.portfolio-filters {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-3xl);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-full);
  background-color: transparent;
  border: 2px solid var(--border-color);
  color: var(--text-secondary);
  font-weight: 500;
  transition: var(--transition-base);
  cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.portfolio-item {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: var(--transition-base);
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-heavy);
}

.portfolio-image {
  position: relative;
  aspect-ratio: 4/3;
  overflow: hidden;
}

.portfolio-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
}

.portfolio-item:hover .portfolio-image img {
  transform: scale(1.1);
}

.portfolio-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(31, 103, 241, 0.9), rgba(224, 241, 31, 0.9));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-base);
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-info {
  text-align: center;
  color: white;
  padding: var(--spacing-lg);
}

.portfolio-info h4 {
  font-size: var(--fs-2xl);
  margin-bottom: var(--spacing-sm);
}

.portfolio-info p {
  margin-bottom: var(--spacing-lg);
  opacity: 0.9;
}

.portfolio-link {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: white;
  color: var(--primary-color);
  border-radius: var(--radius-full);
  font-weight: 500;
  transition: var(--transition-base);
}

.portfolio-link:hover {
  background-color: var(--primary-color);
  color: white;
}

/* ===== SERVICES SECTION ===== */
.services {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background-dark);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.service-card {
  background-color: var(--background-light);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-light);
  transition: var(--transition-base);
  text-align: center;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-heavy);
}

.service-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.service-card h3 {
  font-size: var(--fs-2xl);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.service-card > p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.7;
}

.service-features {
  text-align: left;
}

.service-features li {
  padding: var(--spacing-xs) 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--spacing-lg);
}

.service-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--spacing-3xl) 0;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: start;
}

.contact-info h3 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.contact-info > p {
  font-size: var(--fs-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.7;
}

.contact-details {
  margin-bottom: var(--spacing-xl);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.contact-icon {
  font-size: var(--fs-2xl);
  width: 50px;
  text-align: center;
}

.contact-item h4 {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.contact-item a {
  color: var(--primary-color);
  transition: var(--transition-base);
}

.contact-item a:hover {
  text-decoration: underline;
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.social-link {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--background-dark);
  border-radius: var(--radius-full);
  color: var(--text-secondary);
  font-weight: 500;
  transition: var(--transition-base);
}

.social-link:hover {
  background-color: var(--primary-color);
  color: white;
}

.contact-form {
  background-color: var(--background-dark);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-light);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--fs-base);
  background-color: var(--background-light);
  color: var(--text-primary);
  transition: var(--transition-base);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(31, 103, 241, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--text-primary);
  color: var(--background-light);
  padding: var(--spacing-2xl) 0;
  text-align: center;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.footer-logo a {
  font-family: var(--font-accent);
  font-size: var(--fs-2xl);
  font-weight: 500;
  color: var(--background-light);
}

.footer-text {
  color: rgba(255, 255, 255, 0.7);
  max-width: 400px;
}

.footer-links {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  transition: var(--transition-base);
}

.footer-links a:hover {
  color: var(--primary-light);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-2xl);
  }

  .hero-image {
    order: -1;
  }

  .hero-avatar {
    width: 250px;
    height: 250px;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .about-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: var(--background-light);
    width: 100%;
    text-align: center;
    transition: var(--transition-base);
    box-shadow: var(--shadow-medium);
    padding: var(--spacing-lg) 0;
    gap: var(--spacing-md);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active .bar:nth-child(1) {
    transform: translateY(7px) rotate(45deg);
  }

  .nav-toggle.active .bar:nth-child(3) {
    transform: translateY(-7px) rotate(-45deg);
  }

  .hero-name {
    font-size: var(--fs-5xl);
  }

  .hero-role {
    font-size: var(--fs-2xl);
  }

  .hero-buttons {
    justify-content: center;
  }

  .portfolio-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .about-stats {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .section-title {
    font-size: var(--fs-3xl);
  }

  .footer-content {
    gap: var(--spacing-md);
  }

  .footer-links {
    justify-content: center;
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  .hero {
    padding: var(--spacing-2xl) 0;
  }

  .hero-name {
    font-size: var(--fs-4xl);
  }

  .hero-avatar {
    width: 200px;
    height: 200px;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 250px;
  }

  .portfolio-filters {
    gap: var(--spacing-sm);
  }

  .filter-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--fs-sm);
  }

  .service-card {
    padding: var(--spacing-lg);
  }

  .contact-form {
    padding: var(--spacing-lg);
  }

  .social-links {
    justify-content: center;
  }
}

/* ===== UTILITY ANIMATIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* Focus styles for better accessibility */
a:focus,
button:focus,
input:focus,
textarea:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.5);
  }
}
