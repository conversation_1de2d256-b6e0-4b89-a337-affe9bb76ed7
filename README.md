# Villo - Personal Portfolio Template

A sleek, modern portfolio template designed for creatives to showcase their work with style. This is a clean, human-written version converted from a Framer template.

## Features

- **Modern Design**: Clean, minimalist design with smooth animations
- **Fully Responsive**: Works perfectly on all devices and screen sizes
- **Performance Optimized**: Fast loading with optimized code and assets
- **Accessibility First**: Built with accessibility best practices
- **SEO Friendly**: Semantic HTML and proper meta tags
- **Cross-browser Compatible**: Works on all modern browsers
- **Easy to Customize**: Well-organized code structure for easy modifications

## Sections

- **Hero Section**: Eye-catching introduction with call-to-action buttons
- **About Section**: Personal information, skills, and statistics
- **Portfolio Section**: Filterable project showcase
- **Services Section**: What you offer to clients
- **Contact Section**: Contact form and information
- **Footer**: Links and copyright information

## Technologies Used

- **HTML5**: Semantic markup
- **CSS3**: Modern styling with CSS Grid, Flexbox, and custom properties
- **Vanilla JavaScript**: Interactive functionality without dependencies
- **Google Fonts**: DM Sans, Inter, and Manrope fonts

## Getting Started

1. **Download or clone** this repository
2. **Replace placeholder content** with your own information
3. **Add your images** to the `assets` folder
4. **Customize colors and fonts** in the CSS variables
5. **Deploy** to your preferred hosting platform

## File Structure

```
villo-portfolio/
├── index-clean.html          # Main HTML file
├── styles.css               # All CSS styles
├── script.js               # JavaScript functionality
├── assets/                 # Images and other assets
│   ├── favicon.png
│   ├── hero-avatar.jpg
│   ├── project-1.jpg
│   ├── project-2.jpg
│   ├── project-3.jpg
│   ├── project-4.jpg
│   ├── project-5.jpg
│   └── project-6.jpg
└── README.md              # This file
```

## Customization

### Colors
Edit the CSS variables in `styles.css`:
```css
:root {
  --primary-color: #1f67f1;
  --primary-light: #e0f11f;
  --text-primary: #121212;
  /* ... more variables */
}
```

### Fonts
Change the Google Fonts import in the HTML head and update the CSS variables:
```css
:root {
  --font-primary: 'Your Font', sans-serif;
  --font-secondary: 'Your Font', sans-serif;
}
```

### Content
1. Update personal information in `index-clean.html`
2. Replace placeholder images in the `assets` folder
3. Modify portfolio projects and services
4. Update contact information and social links

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+ (with some limitations)

## Performance Features

- **Optimized Images**: Proper image sizing and formats
- **Minimal Dependencies**: No external libraries except Google Fonts
- **Efficient CSS**: Uses modern CSS features for better performance
- **Lazy Loading**: Images load as needed
- **Minification Ready**: Code structure ready for minification

## Accessibility Features

- **Semantic HTML**: Proper heading hierarchy and landmarks
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: ARIA labels and descriptions
- **High Contrast Support**: Respects user preferences
- **Reduced Motion**: Respects prefers-reduced-motion setting

## SEO Features

- **Meta Tags**: Proper title, description, and Open Graph tags
- **Structured Data**: Schema markup ready
- **Semantic HTML**: Search engine friendly structure
- **Fast Loading**: Optimized for Core Web Vitals

## License

This template is free to use for personal and commercial projects. No attribution required, but appreciated!

## Support

If you have any questions or need help customizing the template, feel free to reach out or create an issue.

## Credits

- **Original Design**: Inspired by modern portfolio trends
- **Fonts**: Google Fonts (DM Sans, Inter, Manrope)
- **Icons**: Custom SVG icons
- **Images**: Placeholder images (replace with your own)

---

**Made with ❤️ and lots of coffee**

Enjoy building your amazing portfolio! 🚀
